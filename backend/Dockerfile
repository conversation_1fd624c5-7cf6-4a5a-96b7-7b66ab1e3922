# Stage 1: Builder - Install dependencies and build custom provider
FROM python:3.10-slim as builder

WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    curl \
    git \
    build-essential \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Go
RUN wget https://go.dev/dl/go1.21.5.linux-amd64.tar.gz \
    && tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz \
    && rm go1.21.5.linux-amd64.tar.gz
ENV PATH="/usr/local/go/bin:$PATH"

# Install Terraform
RUN wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg > /dev/null \
    && gpg --no-default-keyring --keyring /usr/share/keyrings/hashicorp-archive-keyring.gpg --fingerprint \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com bookworm main" | tee /etc/apt/sources.list.d/hashicorp.list \
    && apt-get update \
    && apt-get install -y terraform \
    && rm -rf /var/lib/apt/lists/*

# Clone and build custom provider
RUN mkdir -p submodules \
    && cd submodules \
    && git clone https://github.com/oidebrett/terraform-provider-komodo.git terraform-provider-komodo \
    && cd terraform-provider-komodo \
    && go mod tidy \
    && go build -o bin/terraform-provider-komodo-provider

# Create Python virtual environment and install dependencies
COPY requirements.txt ./
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir -r requirements.txt

# Stage 2: Final Image - Copy app, dependencies, and tools
FROM python:3.10-slim

WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Terraform in final image
RUN wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg > /dev/null \
    && gpg --no-default-keyring --keyring /usr/share/keyrings/hashicorp-archive-keyring.gpg --fingerprint \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com bookworm main" | tee /etc/apt/sources.list.d/hashicorp.list \
    && apt-get update \
    && apt-get install -y terraform \
    && rm -rf /var/lib/apt/lists/*

# Copy the virtual environment from the builder stage
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set up Terraform configuration and custom provider
RUN mkdir -p /root/.terraform.d/plugins/registry.example.com/mattercoder/komodo-provider/1.0.0/linux_amd64 \
    && mkdir -p /root/.terraform.d/plugin-cache

# Copy custom provider binary
COPY --from=builder /app/submodules/terraform-provider-komodo/bin/terraform-provider-komodo-provider /root/.terraform.d/plugins/registry.example.com/mattercoder/komodo-provider/1.0.0/linux_amd64/

# Create .terraformrc configuration
RUN echo 'plugin_cache_dir = "/root/.terraform.d/plugin-cache"' > /root/.terraformrc

# Generate SSH keys (no passphrase)
RUN mkdir -p /root/.ssh && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_awslightsail -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_azure -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_digitalocean -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_hetzner -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_linode -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_vultr -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/tf_ovhcloud -N "" && \
    ssh-keygen -b 4096 -t rsa -f /root/.ssh/id_rsa_unencrypted -N "" && \
    chmod 600 /root/.ssh/*

# Copy the application code
COPY ./app ./app
COPY ./config ./config

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
