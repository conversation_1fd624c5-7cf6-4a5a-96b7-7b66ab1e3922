import os
from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from app.core.deployment.providers.base import ProviderStrategy
from app.models.deployment import Deployment as DeploymentModel
from app.core.config import get_settings
from app.core.deployment.package_utils import normalize_package_name_for_hostname

class OVHcloudStrategy(ProviderStrategy):
    """Concrete strategy for OVHcloud deployments."""

    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        """Generate all necessary Terraform files for an OVHcloud deployment."""
        template_dir = Path(__file__).parent.parent / "terraform_templates"
        env = Environment(loader=FileSystemLoader(template_dir))

        self._generate_ovhcloud_main_files(deployment_dir, env, deployment)
        self._generate_tfvars(deployment_dir, env, deployment)
        self._generate_config_template(deployment_dir, env, deployment)

    def _generate_ovhcloud_main_files(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate main OVHcloud Terraform files from templates."""
        main_template = env.get_template("ovhcloud_main.tf.j2")
        variables_template = env.get_template("ovhcloud_variables.tf.j2")
        outputs_template = env.get_template("ovhcloud_outputs.tf.j2")
        startup_script_template = env.get_template("ovhcloud_startup-script.sh.j2")

        with open(deployment_dir / "main.tf", "w") as f:
            f.write(main_template.render(package=deployment.package))
        with open(deployment_dir / "variables.tf", "w") as f:
            f.write(variables_template.render(package=deployment.package))
        with open(deployment_dir / "outputs.tf", "w") as f:
            f.write(outputs_template.render(package=deployment.package))
        with open(deployment_dir / "startup-script.sh", "w") as f:
            f.write(startup_script_template.render(
                package=deployment.package,
                komodo_provider_ip=get_settings().KOMODO_PROVIDER_IP,
                komodo_passkey=get_settings().KOMODO_PASSKEY
            ))

    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the terraform.tfvars file for OVHcloud."""
        ssh_public_key = ""
        use_user_ssh_key = False
        settings = get_settings()
        ssh_strategy = settings.get_ssh_key_strategy(deployment.cloud_provider)

        # First priority: Use user's SSH key if provided
        if deployment.user_ssh_key:
            ssh_public_key = deployment.user_ssh_key.strip()
            use_user_ssh_key = True
            print("Using user-provided SSH key")
        else:
            # Strategy-based SSH key handling
            if ssh_strategy == "ENV_KEY_PER_DEPLOYMENT":
                # Use provider-specific SSH key file (OVHcloud doesn't have SSH key resources)
                try:
                    ssh_public_key = settings.get_provider_ssh_key("OVHcloud")
                    use_user_ssh_key = True  # Create new resource with provider key
                    print(f"No user SSH key provided, using OVHcloud-specific SSH key")
                except ValueError as e:
                    raise ValueError(f"OVHcloud SSH key configuration error: {e}")
            else:  # SHARED_KEY_REFERENCE
                # This shouldn't happen for OVHcloud, but handle gracefully
                print(f"No user SSH key provided, using SHARED_KEY_REFERENCE strategy")
                use_user_ssh_key = False

        client_name_lower = normalize_package_name_for_hostname(deployment.client_name)

        template = env.get_template("ovhcloud_terraform.tfvars.j2")
        tfvars_content = template.render(
            os_auth_url=get_settings().OS_AUTH_URL,
            os_project_domain_name=get_settings().OS_PROJECT_DOMAIN_NAME,
            os_tenant_name=get_settings().OS_TENANT_NAME,
            os_username=get_settings().OS_USERNAME,
            os_tenant_id=get_settings().OS_TENANT_ID,
            os_user_domain_name=get_settings().OS_USER_DOMAIN_NAME,
            os_password=get_settings().OS_PASSWORD,
            os_identity_api_version=get_settings().OS_IDENTITY_API_VERSION,
            os_region_name=deployment.region,
            flavor_name=deployment.instance_type,
            image_name="Ubuntu 24.04",
            instance_name=f"{client_name_lower}-instance",
            ssh_public_key=ssh_public_key,
            use_user_ssh_key=use_user_ssh_key,
            komodo_provider_endpoint=get_settings().KOMODO_PROVIDER_ENDPOINT,
            komodo_api_key=get_settings().KOMODO_API_KEY,
            komodo_api_secret=get_settings().KOMODO_API_SECRET,
            github_token=get_settings().GITHUB_TOKEN,
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            github_repo=deployment.github_repo,
            traefik_subdomain=deployment.traefik_subdomain,
            middleware_manager_subdomain=deployment.middleware_manager_subdomain,
            nlweb_subdomain=deployment.nlweb_subdomain,
            logs_subdomain=deployment.logs_subdomain,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            client_id=deployment.client_id,
            client_name=deployment.client_name,
            client_name_lower=normalize_package_name_for_hostname(deployment.client_name),
            openai_api_key=deployment.openai_api_key,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey
        )
        with open(deployment_dir / "terraform.tfvars", "w") as f:
            f.write(tfvars_content)

    def _generate_config_template(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the config-template.toml file."""
        template = env.get_template("ovhcloud_config-template.toml.j2")
        content = template.render(
            client_name=deployment.client_name,
            client_name_lower=normalize_package_name_for_hostname(deployment.client_name),
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            github_repo=deployment.github_repo,
            traefik_subdomain=deployment.traefik_subdomain,
            middleware_manager_subdomain=deployment.middleware_manager_subdomain,
            nlweb_subdomain=deployment.nlweb_subdomain,
            logs_subdomain=deployment.logs_subdomain,
            setup_token=deployment.setup_token,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey,
            openai_api_key=deployment.openai_api_key,
            support_level=deployment.support_level,
        )
        with open(deployment_dir / "config-template.toml", "w") as f:
            f.write(content)
