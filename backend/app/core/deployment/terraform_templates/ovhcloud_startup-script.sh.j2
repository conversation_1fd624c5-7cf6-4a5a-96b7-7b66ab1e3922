#!/bin/bash
set -e

# Log all output to a file for debugging
exec > >(tee /var/log/user-data.log) 2>&1

echo "Starting user data script execution at $(date)"

# --- OPTIMIZATION 1 & 2: CONSOLIDATE APT INSTALLS ---

# 1. Update system packages
apt-get update

# 2. Install required packages (includes Python/Pip and core tools)
# Note: python3-pip depends on python3, so installing pip is sufficient.
apt-get install -y \
  ca-certificates \
  curl \
  gnupg \
  lsb-release \
  git \
  postgresql-client \
  tesseract-ocr \
  libtesseract-dev \
  python3-pip

# Add Docker's official GPG key and repository setup
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# 3. Update again and install Docker Engine
apt-get update
# Note: Removed the separate curl install for docker-compose as the plugin is used.
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Create docker group and add default user
usermod -aG docker root

# Create komodo directory structure
mkdir -p /etc/komodo/stacks

# Install Python required packages
pip3 install --break-system-packages requests toml pyyaml

echo "Starting komodo periphery at $(date)"
# Run setup script for komodo periphery
# Note: Using 'sudo python3' might be unnecessary if you run as root/sudo already, but kept for safety.
curl -sSL https://raw.githubusercontent.com/moghtech/komodo/main/scripts/setup-periphery.py | sudo python3

# Configure periphery with allowed IPs and passkeys
sudo sed -i 's/allowed_ips = \[\]/allowed_ips = ["{{ komodo_provider_ip }}"]/' /etc/komodo/periphery.config.toml
sudo sed -i 's/passkeys = \[\]/passkeys = ["{{ komodo_passkey }}"]/' /etc/komodo/periphery.config.toml

# Enable periphery service for future boots
sudo systemctl enable periphery

# Restart periphery service to apply configuration changes
# Assumes 'setup-periphery.py' created the service unit file.
sudo systemctl daemon-reload # Good practice after a service unit file is created/modified
sudo systemctl restart periphery

# --- OPTIMIZATION 7: STREAMLINE UFW ---
# Set up firewall (ufw)
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8120/tcp
sudo ufw allow 9120/tcp
sudo ufw allow 51820/udp
sudo ufw --force enable # Enable ufw at the end

{% if package in ["Coolify", "Coolify+"] %}
# Coolify-specific setup
echo "Setting up Coolify directories and configuration at $(date)"

# Create the base directories for Coolify under /data/coolify
# Use one mkdir -p command to reduce calls
sudo mkdir -p \
  /data/coolify/{source,ssh,applications,databases,backups,services,proxy,webhooks-during-maintenance} \
  /data/coolify/ssh/{keys,mux} \
  /data/coolify/proxy/dynamic

# Generate an SSH key for Coolify to manage your server
# Using 'tee -a' avoids a full cat read/write
sudo ssh-keygen -f /data/coolify/ssh/keys/<EMAIL> -t ed25519 -N '' -C root@coolify

# Add the public key to authorized_keys
# Use tee -a instead of cat >> to ensure sudo is used for the redirection
sudo cat /data/coolify/ssh/keys/<EMAIL> | sudo tee -a ~/.ssh/authorized_keys > /dev/null
sudo chmod 600 ~/.ssh/authorized_keys

# Set the correct permissions for the Coolify files and directories
sudo chown -R 9999:root /data/coolify
sudo chmod -R 700 /data/coolify

# Ensure the Docker network is created
# Using '|| true' or '|| : ' makes the script robust if the network already exists
sudo docker network create --attachable coolify || :

echo "Coolify setup completed at $(date)"
{% endif %}

echo "User data script finished at $(date)"