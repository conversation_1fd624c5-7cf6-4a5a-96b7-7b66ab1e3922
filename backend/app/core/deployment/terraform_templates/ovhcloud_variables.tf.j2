# OVHcloud OpenStack Provider Variables
variable "os_auth_url" {
  description = "OpenStack authentication URL"
  type        = string
  default     = "https://auth.cloud.ovh.net/v3"
}

variable "os_project_domain_name" {
  description = "OpenStack project domain name"
  type        = string
  default     = "Default"
}

variable "os_tenant_name" {
  description = "OpenStack tenant name"
  type        = string
}

variable "os_username" {
  description = "OpenStack username"
  type        = string
}

variable "os_tenant_id" {
  description = "OpenStack tenant ID"
  type        = string
}

variable "os_user_domain_name" {
  description = "OpenStack user domain name"
  type        = string
  default     = "Default"
}

variable "os_password" {
  description = "OpenStack password"
  type        = string
  sensitive   = true
}

variable "os_identity_api_version" {
  description = "OpenStack identity API version"
  type        = string
  default     = "3"
}

variable "os_region_name" {
  description = "OVHcloud region (e.g., BHS5, DE1, GRA9)"
  type        = string
}

variable "flavor_name" {
  description = "OVHcloud instance flavor (e.g., d2-2, d2-4, d2-8)"
  type        = string
}

variable "image_name" {
  description = "Operating system image"
  type        = string
  default     = "Ubuntu 24.04"
}

variable "instance_name" {
  description = "Name of the server instance"
  type        = string
}

variable "client_name" {
  description = "Name of the client"
  type        = string
}

variable "client_name_lower" {
  description = "Lowercase name of the client"
  type        = string
  default     = ""
}

variable "client_id" {
  description = "ID of the client"
  type        = string
}

variable "ssh_public_key" {
  description = "SSH public key for server access (only used when use_user_ssh_key is true)"
  type        = string
  default     = ""
}

variable "use_user_ssh_key" {
  description = "Whether to use user-provided SSH key (true) or reference shared SSH key (false)"
  type        = bool
  default     = false
}

variable "package" {
  description = "Package type for the deployment"
  type        = string
}

# Komodo provider configuration variables
variable "komodo_provider_endpoint" {
  description = "Komodo provider API endpoint"
  type        = string
}

variable "komodo_api_key" {
  description = "Komodo API key"
  type        = string
  sensitive   = true
}

variable "komodo_api_secret" {
  description = "Komodo API secret"
  type        = string
  sensitive   = true
}

variable "github_token" {
  description = "GitHub token for authentication"
  type        = string
  sensitive   = true
}

# Application configuration variables
variable "domain" {
  description = "Domain for the application"
  type        = string
}

variable "admin_email" {
  description = "Email for the admin user"
  type        = string
}

variable "admin_username" {
  description = "Username for the admin user"
  type        = string
}

variable "admin_password" {
  description = "Password for the admin user"
  type        = string
  sensitive   = true
}

variable "admin_subdomain" {
  description = "Subdomain for the admin interface"
  type        = string
  default     = "admin"
}

variable "github_repo" {
  description = "GitHub repository for the application"
  type        = string
}

variable "traefik_subdomain" {
  description = "Traefik subdomain"
  type        = string
}

variable "middleware_manager_subdomain" {
  description = "Middleware manager subdomain"
  type        = string
}

variable "nlweb_subdomain" {
  description = "NLWeb subdomain"
  type        = string
}

variable "logs_subdomain" {
  description = "Logs subdomain"
  type        = string
}

{% if package in ["Pangolin+", "Pangolin+AI"] %}
variable "crowdsec_enrollment_key" {
  description = "CrowdSec Enrollment key"
  type        = string
  sensitive   = true
}

variable "static_page_subdomain" {
  description = "Static page subdomain"
  type        = string
}

variable "maxmind_license_key" {
  description = "MaxMind license key for GeoIP"
  type        = string
  sensitive   = true
}
{% endif %}

{% if package == "Pangolin+AI" %}
variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  default     = ""
  sensitive   = true
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  default     = ""
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  default     = ""
  sensitive   = true
}

variable "komodo_host_ip" {
  description = "Komodo host IP address"
  type        = string
  default     = ""
}

variable "komodo_passkey" {
  description = "Komodo passkey"
  type        = string
  default     = ""
  sensitive   = true
}
{% endif %}
