# OVHcloud OpenStack Configuration
os_auth_url = "{{ os_auth_url }}"
os_project_domain_name = "{{ os_project_domain_name }}"
os_tenant_name = "{{ os_tenant_name }}"
os_username = "{{ os_username }}"
os_tenant_id = "{{ os_tenant_id }}"
os_user_domain_name = "{{ os_user_domain_name }}"
os_password = "{{ os_password }}"
os_identity_api_version = "{{ os_identity_api_version }}"
os_region_name = "{{ os_region_name }}"
flavor_name = "{{ flavor_name }}"
image_name = "{{ image_name }}"
instance_name = "{{ instance_name }}"
ssh_public_key = "{{ ssh_public_key }}"
use_user_ssh_key = {{ use_user_ssh_key | lower }}

# Client Information
client_name = "{{ client_name }}"
client_name_lower = "{{ client_name_lower }}"
client_id = "{{ client_id }}"
package = "{{ package }}"

# Komodo Configuration
komodo_provider_endpoint = "{{ komodo_provider_endpoint }}"
komodo_api_key = "{{ komodo_api_key }}"
komodo_api_secret = "{{ komodo_api_secret }}"
github_token = "{{ github_token }}"

# Application Configuration
domain = "{{ domain }}"
admin_email = "{{ admin_email }}"
admin_username = "{{ admin_username }}"
admin_password = "{{ admin_password }}"
admin_subdomain = "{{ admin_subdomain }}"
github_repo = "{{ github_repo }}"
traefik_subdomain = "{{ traefik_subdomain }}"
middleware_manager_subdomain = "{{ middleware_manager_subdomain }}"
nlweb_subdomain = "{{ nlweb_subdomain }}"
logs_subdomain = "{{ logs_subdomain }}"

{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Premium Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key }}"
static_page_subdomain = "{{ static_page_subdomain }}"
maxmind_license_key = "{{ maxmind_license_key }}"
{% endif %}

{% if package == "Pangolin+AI" %}
# AI Package Configuration
oauth_client_id = "{{ oauth_client_id }}"
oauth_client_secret = "{{ oauth_client_secret }}"
openai_api_key = "{{ openai_api_key }}"
komodo_host_ip = "{{ komodo_host_ip }}"
komodo_passkey = "{{ komodo_passkey }}"
{% endif %}
