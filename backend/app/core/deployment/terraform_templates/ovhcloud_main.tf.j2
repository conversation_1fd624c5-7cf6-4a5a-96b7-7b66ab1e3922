# Terraform configuration for OVHcloud
terraform {
  required_providers {
    openstack = {
      source  = "terraform-provider-openstack/openstack"
      version = ">= 3.0.0"
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

# Configure the OpenStack provider hosted by OVHcloud
provider "openstack" {
  auth_url         = var.os_auth_url
  domain_name      = var.os_project_domain_name
  tenant_name      = var.os_tenant_name
  user_name        = var.os_username
  tenant_id        = var.os_tenant_id
  user_domain_name = var.os_user_domain_name
  password         = var.os_password
  region           = var.os_region_name
}

# Conditionally create SSH key resource if user provided one
resource "openstack_compute_keypair_v2" "default" {
  count      = var.use_user_ssh_key ? 1 : 0
  name       = "terraform-key-${var.client_name_lower}"
  public_key = var.ssh_public_key
}

# Reference existing shared SSH key if user didn't provide one
data "openstack_compute_keypair_v2" "shared" {
  count = var.use_user_ssh_key ? 0 : 1
  name  = "terraform-key"
}

# Creating the instance
resource "openstack_compute_instance_v2" "main" {
  name        = var.instance_name
  image_name  = var.image_name
  flavor_name = var.flavor_name
  key_pair    = var.use_user_ssh_key ? openstack_compute_keypair_v2.default[0].name : data.openstack_compute_keypair_v2.shared[0].name

  network {
    name = "Ext-Net"
  }

  user_data = file("${path.module}/startup-script.sh")

  lifecycle {
    # OVHcloud regularly updates the base image of a given OS so that customer has less packages to update after spawning a new instance
    # To avoid terraform to have some issue with that, the following ignore_changes is required.
    ignore_changes = [
      image_name
    ]
  }

  tags = [var.client_name_lower]
}

# Custom User Provider
provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
  github_orgname = "ManidaeCloud"  
}

# Custom provider resource with templated configuration
resource "komodo-provider_user" "client_syncresources" {
  depends_on = [openstack_compute_instance_v2.main]
  id                = var.client_id
  name              = var.client_name
  generate_ssh_keys = true
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name             = var.client_name
    domain                  = var.domain
    admin_email             = var.admin_email
    admin_username          = var.admin_username
    admin_password          = var.admin_password
    admin_subdomain         = var.admin_subdomain
    github_repo             = var.github_repo
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip = openstack_compute_instance_v2.main.access_ip_v4
}


