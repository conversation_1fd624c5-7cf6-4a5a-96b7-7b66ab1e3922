output "instance_id" {
  description = "ID of the created instance"
  value       = openstack_compute_instance_v2.main.id
}

output "instance_name" {
  description = "Name of the created instance"
  value       = openstack_compute_instance_v2.main.name
}

output "instance_ip" {
  description = "IPv4 address of the created instance"
  value       = openstack_compute_instance_v2.main.access_ip_v4
}

output "instance_flavor" {
  description = "Flavor of the created instance"
  value       = openstack_compute_instance_v2.main.flavor_name
}

output "instance_image" {
  description = "Image of the created instance"
  value       = openstack_compute_instance_v2.main.image_name
}

output "instance_region" {
  description = "Region of the created instance"
  value       = var.os_region_name
}
